<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\ExpenseCategory;
use App\Enums\VendorAuthenticationKind;
use App\Enums\VendorType;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\AsEnumCollection;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property-read \Illuminate\Support\Collection<int, IntegrationPoint> $integration_points
 */
final class Vendor extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'name',
        'type',
        'expense_category',
        'purchase_order_email',
        'new_account_email',
        'phone_number',
        'credentials_schema',
        'is_enabled',
        'authentication_kind',
        'authentication_configuration',
    ];

    /**
     * Get the account receivable contacts for the vendor.
     */
    public function accountReceivableContacts(): HasMany
    {
        return $this->hasMany(VendorAccountReceivableContact::class);
    }

    /**
     * Get the shipping terms for the vendor.
     */
    public function shippingTerms(): HasOne
    {
        return $this->hasOne(VendorShippingTerms::class);
    }

    /**
     * Get the clinic-specific shipping settings for the vendor.
     */
    public function vendorClinicShippingSettings(): HasMany
    {
        return $this->hasMany(VendorClinicShippingSetting::class);
    }

    /**
     * Get the integration connections for the vendor.
     */
    public function integrationConnections(): HasMany
    {
        return $this->hasMany(IntegrationConnection::class);
    }

    /**
     * Get the clinics connected to this vendor through integration connections.
     */
    public function clinics(): HasManyThrough
    {
        return $this->hasManyThrough(
            Clinic::class,
            IntegrationConnection::class,
            'vendor_id',
            'id',
            'id',
            'clinic_id'
        );
    }

    /**
     * Get the products for the vendor.
     */
    public function productOffers(): HasMany
    {
        return $this->hasMany(ProductOffer::class, 'vendor_id', 'id');
    }

    /**
     * Get the suborders for the vendor.
     */
    public function suborders(): HasMany
    {
        return $this->hasMany(SubOrder::class);
    }

    /**
     * Get shipping terms for a specific clinic with fallback to vendor defaults.
     */
    public function getShippingTerms(Clinic $clinic): VendorClinicShippingSetting|VendorShippingTerms|null
    {
        // First, check for clinic-specific settings
        $clinicSetting = $this->vendorClinicShippingSettings()
            ->where('clinic_id', $clinic->id)
            ->first();

        if ($clinicSetting) {
            return $clinicSetting;
        }

        // Fallback to vendor-level settings
        return $this->shippingTerms;
    }

    /**
     * Calculate shipping fee for a specific clinic with vendor fallback.
     *
     * @param  int  $subtotal  Order subtotal in cents
     * @return array<int, int> [amount_needed_for_free_shipping, shipping_fee]
     */
    public function calculateShippingFee(Clinic $clinic, int $subtotal): array
    {
        $shippingTerms = $this->getShippingTerms($clinic);

        if (! $shippingTerms) {
            return [0, 0];
        }

        return $shippingTerms->calculateShippingFee($subtotal);
    }

    public function scopeIsEnabled(Builder $query): Builder
    {
        return $query->where('is_enabled', true);
    }

    public function can(IntegrationPoint $point): bool
    {
        return $this->integration_points->contains($point);
    }

    protected function casts(): array
    {
        return [
            'type' => VendorType::class,
            'expense_category' => ExpenseCategory::class,
            'credentials_schema' => 'array',
            'is_enabled' => 'boolean',
            'authentication_kind' => VendorAuthenticationKind::class,
            'authentication_configuration' => 'array',
            'integration_points' => AsEnumCollection::of(IntegrationPoint::class),
        ];
    }

    protected function imageUrl(): Attribute
    {
        return Attribute::get(fn () => $this->image_path ? asset("storage/$this->image_path") : null);
    }
}
